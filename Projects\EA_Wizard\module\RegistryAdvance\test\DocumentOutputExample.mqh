//+------------------------------------------------------------------+
//|                                         DocumentOutputExample.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| RegistryAdvance 文檔輸出功能使用示例                             |
//| 本文件展示如何使用 RegistryAdvance 測試模組的文檔輸出功能         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 基本使用示例                                                     |
//+------------------------------------------------------------------+
void BasicDocumentOutputExample()
{
    Print("=== 基本文檔輸出示例 ===");
    
    // 1. 運行所有測試並生成完整文檔
    Print("1. 運行所有測試並生成完整文檔:");
    RunAllRegistryAdvanceTestsWithDocs();
    
    Print("");
    Print("文檔將保存在 MQL4\\Files\\TestReports\\ 目錄中");
    Print("包含完整報告和摘要兩個文件");
}

//+------------------------------------------------------------------+
//| 分類測試文檔輸出示例                                             |
//+------------------------------------------------------------------+
void CategoryTestDocumentExample()
{
    Print("=== 分類測試文檔輸出示例 ===");
    
    // 1. 只運行單元測試並生成文檔
    Print("1. 只運行單元測試並生成文檔:");
    RunRegistryAdvanceUnitTestsWithDocs();
    
    Print("");
    
    // 2. 只運行整合測試並生成文檔
    Print("2. 只運行整合測試並生成文檔:");
    RunRegistryAdvanceIntegrationTestsWithDocs();
    
    Print("");
    Print("分別為單元測試和整合測試生成了獨立的文檔");
}

//+------------------------------------------------------------------+
//| 特定測試類別文檔輸出示例                                         |
//+------------------------------------------------------------------+
void SpecificTestDocumentExample()
{
    Print("=== 特定測試類別文檔輸出示例 ===");
    
    // 1. 只測試 RegistryItem 並生成文檔
    Print("1. 只測試 RegistryItem:");
    RunSpecificRegistryAdvanceTestWithDocs("TestRegistryItem");
    
    Print("");
    
    // 2. 只測試 ItemRegistry 並生成文檔
    Print("2. 只測試 ItemRegistry:");
    RunSpecificRegistryAdvanceTestWithDocs("TestItemRegistry");
    
    Print("");
    
    // 3. 只測試整合場景並生成文檔
    Print("3. 只測試整合場景:");
    RunSpecificRegistryAdvanceTestWithDocs("TestRegistryIntegration");
    
    Print("");
    Print("為每個特定測試類別生成了專門的文檔");
}

//+------------------------------------------------------------------+
//| 自定義文檔輸出選項示例                                           |
//+------------------------------------------------------------------+
void CustomDocumentOptionsExample()
{
    Print("=== 自定義文檔輸出選項示例 ===");
    
    // 1. 只生成完整報告，不生成摘要
    Print("1. 只生成完整報告:");
    RunRegistryAdvanceTestsWithCustomDocs(true, false, "FullReportsOnly");
    
    Print("");
    
    // 2. 只生成摘要，不生成完整報告
    Print("2. 只生成摘要:");
    RunRegistryAdvanceTestsWithCustomDocs(false, true, "SummaryOnly");
    
    Print("");
    
    // 3. 生成到自定義目錄
    Print("3. 生成到自定義目錄:");
    RunRegistryAdvanceTestsWithCustomDocs(true, true, "CustomTestReports");
    
    Print("");
    Print("文檔已保存到不同的自定義目錄中");
}

//+------------------------------------------------------------------+
//| 通過測試顯示選項示例                                             |
//+------------------------------------------------------------------+
void PassedTestsDisplayOptionsExample()
{
    Print("=== 通過測試顯示選項示例 ===");
    
    // 1. 限制顯示前3個通過的測試
    Print("1. 限制顯示前3個通過的測試:");
    RunRegistryAdvanceTestsWithLimitedDisplay(3, "Limited3Tests");
    
    Print("");
    
    // 2. 限制顯示前10個通過的測試
    Print("2. 限制顯示前10個通過的測試:");
    RunRegistryAdvanceTestsWithLimitedDisplay(10, "Limited10Tests");
    
    Print("");
    
    // 3. 無限制顯示所有通過的測試
    Print("3. 無限制顯示所有通過的測試:");
    RunRegistryAdvanceTestsWithUnlimitedDisplay("UnlimitedTests");
    
    Print("");
    
    // 4. 高級自定義選項
    Print("4. 高級自定義選項（顯示前5個通過的測試）:");
    RunRegistryAdvanceTestsWithAdvancedDocs(true, true, "Advanced5Tests", 5);
    
    Print("");
    Print("展示了不同的通過測試顯示選項");
}

//+------------------------------------------------------------------+
//| 完整測試套件文檔輸出示例                                         |
//+------------------------------------------------------------------+
void FullTestSuiteDocumentExample()
{
    Print("=== 完整測試套件文檔輸出示例 ===");
    
    // 運行完整測試套件並生成文檔
    Print("運行完整測試套件並生成文檔:");
    FullRegistryAdvanceTestSuiteWithDocs();
    
    Print("");
    Print("完整測試套件包含:");
    Print("• 環境驗證");
    Print("• 所有測試執行");
    Print("• 性能測試");
    Print("• 完整文檔生成");
}

//+------------------------------------------------------------------+
//| 程式化文檔選項配置示例                                           |
//+------------------------------------------------------------------+
void ProgrammaticDocumentConfigExample()
{
    Print("=== 程式化文檔選項配置示例 ===");
    
    // 創建自定義配置的測試運行器
    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    
    // 配置文檔選項
    runner.SetDocumentOptions(true, true, "ProgrammaticConfig");
    
    // 設置顯示前8個通過的測試
    runner.SetPassedTestsDisplayOptions(8);
    
    // 運行測試
    Print("使用程式化配置運行測試:");
    runner.RunAllTestsWithDocs();
    
    // 清理
    delete runner;
    
    Print("");
    Print("展示了如何程式化配置文檔輸出選項");
}

//+------------------------------------------------------------------+
//| 故障排除示例                                                     |
//+------------------------------------------------------------------+
void TroubleshootingExample()
{
    Print("=== 故障排除示例 ===");
    
    Print("如果文檔生成失敗，請檢查:");
    Print("1. 文件權限: 確保 MQL4\\Files\\ 目錄可寫");
    Print("2. 磁盤空間: 確保有足夠的磁盤空間");
    Print("3. 防毒軟件: 檢查是否阻止文件創建");
    Print("4. 目錄結構: MQL4 會自動創建必要的子目錄");
    Print("");
    
    Print("常見問題解決方案:");
    Print("• 如果看不到文檔，檢查 MQL4\\Files\\ 目錄");
    Print("• 如果文檔內容不完整，檢查測試是否正常執行");
    Print("• 如果文檔格式異常，檢查文件編碼設置");
    Print("• 如果生成速度慢，考慮限制通過測試顯示數量");
}

//+------------------------------------------------------------------+
//| 最佳實踐建議示例                                                 |
//+------------------------------------------------------------------+
void BestPracticesExample()
{
    Print("=== 最佳實踐建議 ===");
    
    Print("文檔輸出最佳實踐:");
    Print("");
    
    Print("1. 開發階段:");
    Print("   • 使用無限制顯示查看所有測試詳情");
    Print("   • 頻繁生成文檔以跟踪進度");
    
    Print("");
    Print("2. 日常測試:");
    Print("   • 使用默認設置（10個通過測試）");
    Print("   • 定期檢查測試報告");
    
    Print("");
    Print("3. 生產環境:");
    Print("   • 使用較小限制（3-5個通過測試）");
    Print("   • 保持文檔簡潔");
    
    Print("");
    Print("4. 調試失敗:");
    Print("   • 使用限制0，只關注失敗的測試");
    Print("   • 生成詳細報告分析問題");
    
    Print("");
    Print("5. 報告展示:");
    Print("   • 使用適中限制（5-10個通過測試）");
    Print("   • 平衡詳細度和可讀性");
}

//+------------------------------------------------------------------+
//| 主要示例函數 - 運行所有示例                                       |
//+------------------------------------------------------------------+
void RunAllDocumentOutputExamples()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║           RegistryAdvance 文檔輸出功能示例                   ║");
    Print("║                      EA_Wizard                               ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 運行所有示例
    BasicDocumentOutputExample();
    Print("");
    
    CategoryTestDocumentExample();
    Print("");
    
    SpecificTestDocumentExample();
    Print("");
    
    CustomDocumentOptionsExample();
    Print("");
    
    PassedTestsDisplayOptionsExample();
    Print("");
    
    FullTestSuiteDocumentExample();
    Print("");
    
    ProgrammaticDocumentConfigExample();
    Print("");
    
    TroubleshootingExample();
    Print("");
    
    BestPracticesExample();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    所有示例執行完成                          ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 快速開始示例                                                     |
//+------------------------------------------------------------------+
void QuickStartExample()
{
    Print("=== 快速開始示例 ===");
    Print("最簡單的使用方式:");
    Print("");
    
    // 最基本的文檔輸出
    RunAllRegistryAdvanceTestsWithDocs();
    
    Print("");
    Print("✅ 文檔已生成到 MQL4\\Files\\TestReports\\ 目錄");
    Print("💡 查看生成的 .txt 文件獲取詳細測試結果");
}
