//+------------------------------------------------------------------+
//|                                           TestDocumentOutputDemo.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"
#include "DocumentOutputExample.mqh"

//+------------------------------------------------------------------+
//| RegistryAdvance 文檔輸出功能演示腳本                             |
//| 這個腳本演示如何使用 RegistryAdvance 測試模組的文檔輸出功能       |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║           RegistryAdvance 文檔輸出功能演示                   ║");
    Print("║                      EA_Wizard                               ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 演示基本文檔輸出功能
    DemoBasicDocumentOutput();
    
    Print("");
    Print("═══════════════════════════════════════════════════════════════");
    Print("");
    
    // 演示自定義文檔輸出選項
    DemoCustomDocumentOptions();
    
    Print("");
    Print("═══════════════════════════════════════════════════════════════");
    Print("");
    
    // 演示通過測試顯示選項
    DemoPassedTestsDisplayOptions();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    演示完成                                  ║");
    Print("║   請檢查 MQL4\\Files\\TestReports\\ 目錄查看生成的文檔        ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("RegistryAdvance 文檔輸出演示結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這個演示腳本不需要處理 tick 事件
}

//+------------------------------------------------------------------+
//| 演示基本文檔輸出功能                                             |
//+------------------------------------------------------------------+
void DemoBasicDocumentOutput()
{
    Print("🎯 演示 1: 基本文檔輸出功能");
    Print("─────────────────────────────────────────────────────────────");
    
    // 1. 運行所有測試並生成完整文檔
    Print("1.1 運行所有測試並生成完整文檔:");
    RunAllRegistryAdvanceTestsWithDocs();
    
    Print("");
    
    // 2. 只運行單元測試並生成文檔
    Print("1.2 只運行單元測試並生成文檔:");
    RunRegistryAdvanceUnitTestsWithDocs();
    
    Print("");
    
    // 3. 運行特定測試並生成文檔
    Print("1.3 運行特定測試並生成文檔:");
    RunSpecificRegistryAdvanceTestWithDocs("TestRegistryItem");
    
    Print("");
    Print("✅ 基本文檔輸出演示完成");
}

//+------------------------------------------------------------------+
//| 演示自定義文檔輸出選項                                           |
//+------------------------------------------------------------------+
void DemoCustomDocumentOptions()
{
    Print("🎯 演示 2: 自定義文檔輸出選項");
    Print("─────────────────────────────────────────────────────────────");
    
    // 1. 只生成完整報告
    Print("2.1 只生成完整報告:");
    RunRegistryAdvanceTestsWithCustomDocs(true, false, "Demo_FullReportOnly");
    
    Print("");
    
    // 2. 只生成摘要
    Print("2.2 只生成摘要:");
    RunRegistryAdvanceTestsWithCustomDocs(false, true, "Demo_SummaryOnly");
    
    Print("");
    
    // 3. 生成到自定義目錄
    Print("2.3 生成到自定義目錄:");
    RunRegistryAdvanceTestsWithCustomDocs(true, true, "Demo_CustomDirectory");
    
    Print("");
    Print("✅ 自定義文檔輸出選項演示完成");
}

//+------------------------------------------------------------------+
//| 演示通過測試顯示選項                                             |
//+------------------------------------------------------------------+
void DemoPassedTestsDisplayOptions()
{
    Print("🎯 演示 3: 通過測試顯示選項");
    Print("─────────────────────────────────────────────────────────────");
    
    // 1. 限制顯示前3個通過的測試
    Print("3.1 限制顯示前3個通過的測試:");
    RunRegistryAdvanceTestsWithLimitedDisplay(3, "Demo_Limited3");
    
    Print("");
    
    // 2. 無限制顯示所有通過的測試
    Print("3.2 無限制顯示所有通過的測試:");
    RunRegistryAdvanceTestsWithUnlimitedDisplay("Demo_Unlimited");
    
    Print("");
    
    // 3. 高級自定義選項
    Print("3.3 高級自定義選項（顯示前5個通過的測試）:");
    RunRegistryAdvanceTestsWithAdvancedDocs(true, true, "Demo_Advanced5", 5);
    
    Print("");
    
    // 4. 程式化配置
    Print("3.4 程式化配置:");
    DemoProgrammaticConfiguration();
    
    Print("");
    Print("✅ 通過測試顯示選項演示完成");
}

//+------------------------------------------------------------------+
//| 演示程式化配置                                                   |
//+------------------------------------------------------------------+
void DemoProgrammaticConfiguration()
{
    Print("   創建自定義配置的測試運行器...");
    
    // 創建自定義配置的測試運行器
    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    
    // 配置文檔選項
    runner.SetDocumentOptions(true, true, "Demo_Programmatic");
    
    // 設置顯示前8個通過的測試
    runner.SetPassedTestsDisplayOptions(8);
    
    // 運行測試
    runner.RunAllTestsWithDocs();
    
    // 清理
    delete runner;
    
    Print("   程式化配置演示完成");
}

//+------------------------------------------------------------------+
//| 演示完整測試套件                                                 |
//+------------------------------------------------------------------+
void DemoFullTestSuite()
{
    Print("🎯 演示 4: 完整測試套件");
    Print("─────────────────────────────────────────────────────────────");
    
    // 運行完整測試套件並生成文檔
    Print("4.1 運行完整測試套件並生成文檔:");
    FullRegistryAdvanceTestSuiteWithDocs();
    
    Print("");
    Print("✅ 完整測試套件演示完成");
}

//+------------------------------------------------------------------+
//| 演示所有文檔輸出示例                                             |
//+------------------------------------------------------------------+
void DemoAllDocumentOutputExamples()
{
    Print("🎯 演示 5: 所有文檔輸出示例");
    Print("─────────────────────────────────────────────────────────────");
    
    // 運行所有文檔輸出示例
    RunAllDocumentOutputExamples();
    
    Print("");
    Print("✅ 所有文檔輸出示例演示完成");
}

//+------------------------------------------------------------------+
//| 快速測試函數 - 用於快速驗證功能                                   |
//+------------------------------------------------------------------+
void QuickTest()
{
    Print("⚡ 快速測試開始...");
    
    // 運行最基本的文檔輸出測試
    RunAllRegistryAdvanceTestsWithDocs();
    
    Print("⚡ 快速測試完成");
    Print("💡 檢查 MQL4\\Files\\TestReports\\ 目錄查看生成的文檔");
}
