# RegistryAdvance 模組測試文檔

## 概述

本目錄包含 RegistryAdvance 模組的完整測試套件，提供全面的單元測試和整合測試，確保註冊器功能的正確性和穩定性。

## 目錄結構

```
test/
├── README.md                           # 本文檔
├── TestFramework.mqh                   # 測試框架
├── TestRunner.mqh                      # 測試運行器
├── RunAllTests.mqh                     # 主測試入口
├── mock/                               # Mock 類別
│   └── MockRegistry.mqh               # Mock 註冊器實現
├── unit/                               # 單元測試
│   ├── TestRegistryItem.mqh           # RegistryItem 測試
│   ├── TestRegistryResult.mqh         # RegistryResult 測試
│   └── TestItemRegistry.mqh           # ItemRegistry 測試
└── integration/                        # 整合測試
    └── TestRegistryIntegration.mqh     # 整合場景測試
```

## 快速開始

### 運行所有測試

```mql4
#include "Projects/EA_Wizard/module/RegistryAdvance/test/RunAllTests.mqh"

void OnInit()
{
    RunAllRegistryAdvanceTests();
}
```

### 運行特定類型的測試

```mql4
// 只運行單元測試
RunRegistryAdvanceUnitTests();

// 只運行整合測試
RunRegistryAdvanceIntegrationTests();

// 快速檢查（靜默模式）
bool allPassed = QuickRegistryAdvanceCheck();
```

### 運行特定測試類別

```mql4
// 測試 RegistryItem
RunSpecificRegistryAdvanceTest("TestRegistryItem");

// 測試 RegistryResult
RunSpecificRegistryAdvanceTest("TestRegistryResult");

// 測試 ItemRegistry
RunSpecificRegistryAdvanceTest("TestItemRegistry");

// 測試整合場景
RunSpecificRegistryAdvanceTest("TestRegistryIntegration");
```

## 測試框架

### TestFramework.mqh

提供基礎的測試功能：

- **Assert 類別**：提供各種斷言方法
- **TestCase 基類**：所有測試類別的基類
- **TestRunner**：管理和執行測試
- **TestResult**：記錄測試結果

### 斷言方法

```mql4
// 布爾斷言
Assert::AssertTrue("測試名稱", condition, "錯誤消息");
Assert::AssertFalse("測試名稱", condition, "錯誤消息");

// 相等斷言
Assert::AssertEquals("測試名稱", expected, actual, "錯誤消息");

// 指針斷言
Assert::AssertNotNull("測試名稱", pointer, "錯誤消息");
Assert::AssertNull("測試名稱", pointer, "錯誤消息");

// 字符串包含斷言
Assert::AssertContains("測試名稱", haystack, needle, "錯誤消息");
Assert::AssertNotContains("測試名稱", haystack, needle, "錯誤消息");
```

## 測試類別

### 1. TestRegistryItem

測試 `RegistryItem` 類別的基本功能：

- 構造函數測試
- Getter 方法測試（GetId, GetName, GetDescription, GetValue, GetType, GetCreateTime）
- ToString 方法測試
- 不同數據類型測試
- 時間戳測試
- 邊界情況和長值測試

### 2. TestRegistryResult

測試 `RegistryResult` 類別的結果處理功能：

- 構造函數測試
- IsSuccess 方法測試
- GetMessage, GetSource, GetKey 方法測試
- 成功和失敗結果場景測試
- 不同鍵類型測試
- 空值和邊界情況測試

### 3. TestItemRegistry

測試 `ItemRegistry` 類別的核心註冊功能：

- 構造函數和基本屬性測試
- Register 方法測試
- Unregister 方法測試
- Find 方法測試
- 最大項目數限制測試
- 重複鍵處理測試
- Clear 方法測試
- GetLastRegisteredKey 測試
- 不同值類型測試

### 4. TestRegistryIntegration

測試整合場景：

- 完整工作流程測試
- 多個註冊器協同工作
- 大規模操作測試
- 錯誤恢復測試
- 併發操作模擬測試
- 數據完整性測試
- 性能場景測試
- 邊界情況測試

## Mock 類別

### MockRegistry

提供用於測試的模擬註冊器：

```mql4
// 創建字符串鍵的 Mock 註冊器
MockRegistry<string, int>* mockRegistry = MockRegistryFactory::CreateStringKeyRegistry<int>("測試");

// 設置失敗場景
mockRegistry.SetShouldFailRegister(true);
mockRegistry.SetCustomFailMessage("測試失敗");

// 檢查調用次數
int callCount = mockRegistry.GetRegisterCallCount();
```

### MockRegistryFactory

提供便捷的 Mock 註冊器創建方法：

- `CreateStringKeyRegistry<Val>()` - 創建字符串鍵註冊器
- `CreateIntKeyRegistry<Val>()` - 創建整數鍵註冊器
- `CreateFailingRegistry<Key, Val>()` - 創建會失敗的註冊器
- `CreateLimitedRegistry<Key, Val>()` - 創建容量限制的註冊器
- `CreatePrefilledStringIntRegistry()` - 創建預填充的註冊器

## 測試最佳實踐

### 1. 測試命名

- 使用描述性的測試名稱
- 包含測試的功能和預期結果
- 使用中文註解說明測試目的

### 2. 測試結構

```mql4
void TestSpecificFeature()
{
    SetUp();        // 準備測試環境
    
    // 執行測試邏輯
    // 使用斷言驗證結果
    
    TearDown();     // 清理測試環境
}
```

### 3. 資源管理

- 在測試中創建的對象必須在測試結束時清理
- 使用 `SetUp()` 和 `TearDown()` 方法管理資源
- 避免記憶體洩漏

### 4. 測試獨立性

- 每個測試應該獨立運行
- 不依賴其他測試的執行順序
- 不共享狀態

## 高級測試功能

### 性能測試

```mql4
void OnInit()
{
    RunRegistryPerformanceTests();
}
```

### 壓力測試

```mql4
void OnInit()
{
    RegistryStressTest();
}
```

### 環境驗證

```mql4
void OnInit()
{
    ValidateRegistryTestEnvironment();
}
```

### 完整測試套件

```mql4
void OnInit()
{
    FullRegistryAdvanceTestSuite();
}
```

## 持續整合

### CI 測試

```mql4
void OnInit()
{
    CIRegistryAdvanceTests();
}
```

### 回歸測試

```mql4
void OnInit()
{
    RunRegistryRegressionTests();
}
```

### 測試報告

```mql4
void OnInit()
{
    GenerateRegistryTestReport();
}
```

## 故障排除

### 常見問題

1. **編譯錯誤**
   - 檢查所有依賴檔案是否正確包含
   - 確認檔案路徑正確
   - 檢查模板語法

2. **測試失敗**
   - 查看詳細的錯誤消息
   - 檢查被測試的代碼是否有變更
   - 驗證測試數據的正確性

3. **記憶體問題**
   - 確保所有 `new` 操作都有對應的 `delete`
   - 檢查指針是否正確初始化
   - 注意模板類別的記憶體管理

### 調試技巧

1. **使用詳細模式**
   ```mql4
   RegistryAdvanceTestRunner* runner = new RegistryAdvanceTestRunner(true, true, true);
   ```

2. **運行單個測試**
   ```mql4
   RunSpecificRegistryAdvanceTest("TestRegistryItem");
   ```

3. **檢查測試環境**
   ```mql4
   ValidateRegistryTestEnvironment();
   ```

4. **開發者快速測試**
   ```mql4
   DevQuickRegistryTest();
   ```

## 擴展測試

### 添加新測試

1. 在適當的目錄創建新的測試檔案
2. 繼承 `TestCase` 基類
3. 實現 `RunTests()` 方法
4. 在 `TestRunner.mqh` 中添加新測試

### 添加新斷言

在 `TestFramework.mqh` 的 `Assert` 類別中添加新方法。

### 添加新 Mock 類別

在 `mock/` 目錄下創建新的 Mock 實現。

## 版本歷史

- **v1.0** - 初始版本
  - 完整的單元測試覆蓋
  - 整合測試場景
  - Mock 類別支援
  - 性能和壓力測試
  - 詳細的測試報告

## 貢獻指南

1. 遵循現有的代碼風格
2. 為新功能添加相應的測試
3. 確保所有測試通過
4. 更新相關文檔
5. 考慮性能影響
